import 'dart:convert';
import 'package:droit/src/widgets/guide_screen.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import 'base_screen.dart';
import 'locale_provider.dart';
import 'NewRequestScreen.dart';
import 'history_screen.dart';
import 'RenewRequest.dart';
import 'notifications_screen.dart';
import 'ExpiredPermitsScreen.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const BaseScreen(
      currentIndex: 0,
      showAppBar: false,
      child: DashboardContent(),
    );
  }
}

class DashboardContent extends StatelessWidget {
  const DashboardContent({super.key});

  Future<String?> _fetchUserName() async {
    final _storage = const FlutterSecureStorage();
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null) {
        print('No authentication token found'); // Debug print
        return await _storage.read(key: 'user_name') ?? 'User'; // Fallback to stored name or 'User'
      }

      // Attempt to fetch user profile from API
      final profileResponse = await http.get(
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/profile'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );
      if (profileResponse.statusCode == 200) {
        final data = jsonDecode(profileResponse.body);
        if (data['success'] == true && data['data'] != null) {
          final userName = data['data']['name'] as String?;
          print('Fetched user name from API: $userName'); // Debug print
          if (userName != null) {
            await _storage.write(key: 'user_name', value: userName); // Cache the name
            return userName;
          }
        }
      } else {
        print('API profile fetch failed with status: ${profileResponse.statusCode}'); // Debug print
      }

      // Fallback to requests endpoint if profile endpoint fails
      final requestsResponse = await http.get(
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/requests'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );
      if (requestsResponse.statusCode == 200) {
        final data = jsonDecode(requestsResponse.body);
        if (data['success'] == true && data['data'] != null) {
          final userData = (data['data'] as List?)?.firstWhere(
            (item) => item['user'] != null,
            orElse: () => {'user': {'name': null}},
          );
          final userName = userData['user']['name'] as String?;
          print('Fetched user name from requests: $userName'); // Debug print
          if (userName != null) {
            await _storage.write(key: 'user_name', value: userName); // Cache the name
            return userName;
          }
        }
      } else {
        print('API requests fetch failed with status: ${requestsResponse.statusCode}'); // Debug print
      }

      // Final fallback to stored name or default
      final storedName = await _storage.read(key: 'user_name');
      print('Falling back to stored name: $storedName'); // Debug print
      return storedName ?? 'User';
    } catch (e) {
      print('Error fetching user name: $e'); // Debug print
      return await _storage.read(key: 'user_name') ?? 'User'; // Fallback in case of error
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.isArabic;
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return SingleChildScrollView(
      child: Column(
        children: [
          FutureBuilder<String?>(
            future: _fetchUserName(),
            builder: (context, snapshot) {
              final userName = snapshot.data;
              final isLoading = snapshot.connectionState == ConnectionState.waiting;

              return _buildHeaderSection(isArabic, context, userName, isLoading, isTablet);
            },
          ),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: isTablet ? 24 : 12,
              vertical: 6,
            ),
            child: Column(
              crossAxisAlignment:
                  isArabic ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 12),
                _buildMainActionsList(isArabic, context, isTablet),
                const SizedBox(height: 12),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(bool isArabic, BuildContext context, String? userName, bool isLoading, bool isTablet) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryOrange,
            AppColors.primaryOrange.withOpacity(0.8),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? 24 : 16,
            vertical: isTablet ? 20 : 16,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isArabic ? 'مرحباً بك' : 'Welcome back',
                          style: ThemeHelper.getSubtitleStyle(context).copyWith(
                            color: AppColors.pureWhite.withOpacity(0.9),
                            fontSize: isTablet ? 16 : 14,
                          ),
                        ),
                        const SizedBox(height: 2),
                        if (isLoading)
                          const CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.pureWhite),
                          )
                        else
                          Text(
                            isArabic ? (userName ?? 'مستخدم') : (userName ?? 'User'),
                            style: ThemeHelper.getTitleStyle(context).copyWith(
                              color: AppColors.pureWhite,
                              fontSize: isTablet ? 24 : 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                      ],
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.pureWhite.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.notifications_outlined,
                        color: AppColors.pureWhite,
                        size: isTablet ? 24 : 20,
                      ),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const NotificationsScreen(),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                AppLocalizations.tr(context, 'app_subtitle') ?? 'Manage your requests efficiently',
                style: ThemeHelper.getSubtitleStyle(context).copyWith(
                  color: AppColors.pureWhite.withOpacity(0.8),
                  fontSize: isTablet ? 14 : 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    IconData icon,
    String value,
    String label,
    Color color,
    bool isTablet,
  ) {
    final colors = ThemeHelper.getColorsWithListener(context);
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: isTablet ? 22 : 18,
        ),
        SizedBox(height: isTablet ? 8 : 6),
        Text(
          value,
          style: ThemeHelper.getSectionTitleStyle(context).copyWith(
            fontSize: isTablet ? 20 : 16,
            fontWeight: FontWeight.bold,
            color: colors.textPrimary,
          ),
        ),
        SizedBox(height: isTablet ? 4 : 2),
        Text(
          label,
          style: ThemeHelper.getSubtitleStyle(context).copyWith(
            fontSize: isTablet ? 12 : 10,
            color: colors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildMainActionsList(
      bool isArabic, BuildContext context, bool isTablet) {
    final spacing = isTablet ? 12.0 : 10.0;

    return Column(
      children: [
        _buildActionListCard(
          context: context,
          icon: Icons.add_circle_outline,
          iconColor: AppColors.success,
          title: AppLocalizations.tr(context, 'new_request') ?? 'New Request',
          subtitle: AppLocalizations.tr(context, 'new_request_subtitle') ?? 'Submit a new permit request',
          buttonText: AppLocalizations.tr(context, 'submit') ?? 'Submit',
          isTablet: isTablet,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const NewRequestScreen()),
            );
          },
        ),
        SizedBox(height: spacing),
        _buildActionListCard(
          context: context,
          icon: Icons.warning_amber_rounded,
          iconColor: AppColors.error,
          title: AppLocalizations.tr(context, 'expired_permits') ?? 'Expired Permits',
          subtitle: AppLocalizations.tr(context, 'renew_expired_permits') ?? 'Renew your expired permits',
          buttonText: AppLocalizations.tr(context, 'view_expired') ?? 'View Expired',
          isTablet: isTablet,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => const ExpiredPermitsScreen()),
            );
          },
        ),
        SizedBox(height: spacing),
        _buildActionListCard(
          context: context,
          icon: Icons.list_alt,
          iconColor: AppColors.warning,
          title: AppLocalizations.tr(context, 'requests') ?? 'Requests',
          subtitle: AppLocalizations.tr(context, 'requests_subtitle') ?? 'View all your requests',
          buttonText: AppLocalizations.tr(context, 'view_requests') ?? 'View Requests',
          isTablet: isTablet,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const HistoryScreen()),
            );
          },
        ),
        SizedBox(height: spacing),
        _buildActionListCard(
          context: context,
          icon: Icons.help_outline,
          iconColor: AppColors.primaryOrange,
          title: AppLocalizations.tr(context, 'user_guide') ?? 'User Guide',
          subtitle: AppLocalizations.tr(context, 'guide_subtitle') ?? 'Learn how to use the app',
          buttonText: AppLocalizations.tr(context, 'view_guide') ?? 'View Guide',
          isTablet: isTablet,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const GuideScreen()),
            );
          },
        ),
      ],
    );
  }

  Widget _buildActionListCard({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required String buttonText,
    required bool isTablet,
    VoidCallback? onTap,
  }) {
    final colors = ThemeHelper.getColorsWithListener(context);
    final borderRadius = isTablet ? 16.0 : 12.0;
    final padding = isTablet ? 16.0 : 14.0;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(borderRadius),
      child: Container(
        decoration: BoxDecoration(
          color: colors.card,
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: [
            BoxShadow(
              color: colors.shadowColor.withOpacity(0.04),
              blurRadius: isTablet ? 12 : 8,
              offset: const Offset(0, 3),
            ),
          ],
          border: Border.all(
            color: iconColor.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(padding),
          child: Row(
            children: [
              // Icon section
              Container(
                padding: EdgeInsets.all(isTablet ? 14 : 12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      iconColor.withOpacity(0.15),
                      iconColor.withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: isTablet ? 24 : 20,
                ),
              ),
              SizedBox(width: isTablet ? 16 : 12),
              // Content section
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: ThemeHelper.getSectionTitleStyle(context).copyWith(
                        fontSize: isTablet ? 16 : 15,
                        fontWeight: FontWeight.w600,
                        color: colors.textPrimary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: isTablet ? 6 : 4),
                    Text(
                      subtitle,
                      style: ThemeHelper.getSubtitleStyle(context).copyWith(
                        fontSize: isTablet ? 13 : 12,
                        color: colors.textSecondary,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              SizedBox(width: isTablet ? 12 : 8),
              // Button section
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isTablet ? 16 : 12,
                  vertical: isTablet ? 10 : 8,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      iconColor,
                      iconColor.withOpacity(0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(isTablet ? 10 : 8),
                  boxShadow: [
                    BoxShadow(
                      color: iconColor.withOpacity(0.25),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  buttonText,
                  style: TextStyle(
                    color: AppColors.pureWhite,
                    fontSize: isTablet ? 13 : 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    BuildContext context,
    IconData icon,
    Color iconColor,
    String title,
    String subtitle,
    String time,
    bool isTablet,
  ) {
    final colors = ThemeHelper.getColorsWithListener(context);
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(isTablet ? 10 : 8),
          decoration: BoxDecoration(
            color: iconColor.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: isTablet ? 16 : 14,
          ),
        ),
        SizedBox(width: isTablet ? 12 : 10),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: ThemeHelper.getSubtitleStyle(context).copyWith(
                  fontSize: isTablet ? 14 : 13,
                  fontWeight: FontWeight.w500,
                  color: colors.textPrimary,
                ),
              ),
              Text(
                subtitle,
                style: ThemeHelper.getSubtitleStyle(context).copyWith(
                  fontSize: isTablet ? 12 : 11,
                  color: colors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        Text(
          time,
          style: ThemeHelper.getSubtitleStyle(context).copyWith(
            fontSize: isTablet ? 11 : 10,
            color: colors.textSecondary,
          ),
        ),
      ],
    );
  }
}