import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import 'RenewRequest.dart';

class ExpiredPermitCard extends StatefulWidget {
  final Map<String, dynamic> permit;
  final VoidCallback? onRenewed;

  const ExpiredPermitCard({
    super.key,
    required this.permit,
    this.onRenewed,
  });

  @override
  _ExpiredPermitCardState createState() => _ExpiredPermitCardState();
}

class _ExpiredPermitCardState extends State<ExpiredPermitCard> {
  final _storage = const FlutterSecureStorage();
  bool _isLoading = false;

  /// Check if permit is expired
  bool get isExpired {
    final expiryDateStr = widget.permit['expiry_date'] ?? widget.permit['expiryDate'];
    if (expiryDateStr == null) return false;
    
    try {
      final expiryDate = DateTime.parse(expiryDateStr.toString());
      return DateTime.now().isAfter(expiryDate);
    } catch (e) {
      print('Error parsing expiry date: $e');
      return false;
    }
  }

  /// Get authentication token
  Future<String?> _getAuthToken() async {
    try {
      return await _storage.read(key: 'auth_token');
    } catch (e) {
      print('Error reading auth token: $e');
      return null;
    }
  }

  /// Validate permit ownership
  Future<bool> _validatePermitOwnership() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final token = await _getAuthToken();
      if (token == null) return false;

      final permitId = widget.permit['id'] ?? widget.permit['permitId'];
      if (permitId == null) return false;

      final response = await http.get(
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/permits/$permitId'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error validating permit ownership: $e');
      return false;
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Navigate to renewal screen
  void _navigateToRenewal() async {
    final isOwner = await _validatePermitOwnership();
    
    if (!mounted) return;
    
    if (!isOwner) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('You can only renew your own permits'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final permitId = widget.permit['id'] ?? widget.permit['permitId'];
    if (permitId != null) {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => RenewRequestScreen(
            permitId: int.tryParse(permitId.toString()) ?? 1,
            permitData: widget.permit,
          ),
        ),
      );
      
      // If renewal was successful, call the callback
      if (result == true && widget.onRenewed != null) {
        widget.onRenewed!();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colors = ThemeHelper.getColors(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    // Only show card if permit is expired
    if (!isExpired) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.only(bottom: isTablet ? 12 : 10),
      decoration: BoxDecoration(
        color: colors.card,
        borderRadius: BorderRadius.circular(isTablet ? 16 : 12),
        boxShadow: [
          BoxShadow(
            color: colors.shadowColor.withOpacity(0.06),
            blurRadius: isTablet ? 12 : 8,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(
          color: AppColors.error.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isTablet ? 12 : 10),
                  decoration: BoxDecoration(
                    color: AppColors.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
                  ),
                  child: Icon(
                    Icons.warning_amber_rounded,
                    color: AppColors.error,
                    size: isTablet ? 28 : 24,
                  ),
                ),
                SizedBox(width: isTablet ? 16 : 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.tr(context, 'permit_number')?.replaceAll(
                              '{number}',
                              '${widget.permit['permitRequestID'] ?? widget.permit['id'] ?? 'N/A'}',
                            ) ??
                            'Permit #${widget.permit['permitRequestID'] ?? widget.permit['id'] ?? 'N/A'}',
                        style: ThemeHelper.getSectionTitleStyle(context).copyWith(
                          fontSize: isTablet ? 16 : 15,
                          fontWeight: FontWeight.w600,
                          color: colors.textPrimary,
                        ),
                      ),
                      SizedBox(height: isTablet ? 4 : 2),
                      Text(
                        AppLocalizations.tr(context, 'permit_expires_soon') ?? 'Permit expired',
                        style: ThemeHelper.getSubtitleStyle(context).copyWith(
                          fontSize: isTablet ? 13 : 12,
                          color: AppColors.error,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: isTablet ? 16 : 12),
            if (widget.permit['expiry_date'] != null || widget.permit['expiryDate'] != null) ...[
              Text(
                AppLocalizations.tr(context, 'issue_date')?.replaceAll(
                      '{date}',
                      widget.permit['expiry_date']?.toString() ?? 
                      widget.permit['expiryDate']?.toString() ?? 'N/A',
                    ) ??
                    'Expiry Date: ${widget.permit['expiry_date'] ?? widget.permit['expiryDate'] ?? 'N/A'}',
                style: ThemeHelper.getSubtitleStyle(context).copyWith(
                  fontSize: isTablet ? 13 : 12,
                  color: colors.textSecondary,
                ),
              ),
              SizedBox(height: isTablet ? 16 : 12),
            ],
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _navigateToRenewal,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryOrange,
                  foregroundColor: AppColors.pureWhite,
                  elevation: 0,
                  padding: EdgeInsets.symmetric(
                    vertical: isTablet ? 12 : 10,
                    horizontal: isTablet ? 16 : 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
                  ),
                ),
                child: _isLoading
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: AppColors.pureWhite,
                          strokeWidth: 2,
                        ),
                      )
                    : Text(
                        AppLocalizations.tr(context, 'renew_request') ?? 'Renew Permit',
                        style: TextStyle(
                          fontSize: isTablet ? 14 : 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
