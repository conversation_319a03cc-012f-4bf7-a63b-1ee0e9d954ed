# Optimisation du Renouvellement de Permis

## Résumé des Améliorations

Ce document décrit les optimisations apportées au système de renouvellement de permis pour une meilleure intégration avec le backend.

## Modifications Principales

### 1. Gestion Dynamique du Token d'Authentification

**Avant :**
- Token hardcodé dans le code
- Risque de sécurité et maintenance difficile

**Après :**
- Récupération dynamique du token depuis `FlutterSecureStorage`
- Validation du token avant chaque requête
- Gestion d'erreur appropriée si le token est manquant

```dart
Future<String?> _getAuthToken() async {
  try {
    return await _storage.read(key: 'auth_token');
  } catch (e) {
    print('Error reading auth token: $e');
    return null;
  }
}
```

### 2. Raison Constante (Masquée de l'UI)

**Avant :**
- Champ raison visible dans l'interface utilisateur
- Utilisateur devait saisir manuellement la raison

**Après :**
- <PERSON>son constante : `"Need more time to complete the project"`
- Champ supprimé de l'interface utilisateur
- Envoi automatique de la raison constante au backend

```dart
static const String _constantReason = 'Need more time to complete the project';

// Dans la requête
request.fields['reason'] = _constantReason;
```

### 3. Validation de la Date d'Expiration

**Nouveau :**
- Vérification automatique que le permis est expiré
- Affichage conditionnel des options de renouvellement
- Widget `ExpiredPermitCard` pour les permis expirés uniquement

```dart
bool get isExpired {
  final expiryDateStr = widget.permit['expiry_date'] ?? widget.permit['expiryDate'];
  if (expiryDateStr == null) return false;
  
  try {
    final expiryDate = DateTime.parse(expiryDateStr.toString());
    return DateTime.now().isAfter(expiryDate);
  } catch (e) {
    return false;
  }
}
```

### 4. Validation de Propriété du Permis

**Nouveau :**
- Vérification que le permis appartient à l'utilisateur authentifié
- Appel API pour valider la propriété avant le renouvellement
- Message d'erreur approprié : "You can only renew your own permits"

```dart
Future<bool> _validatePermitOwnership() async {
  final token = await _getAuthToken();
  if (token == null) return false;

  final response = await http.get(
    Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/permits/$permitId'),
    headers: {
      'Authorization': 'Bearer $token',
      'Accept': 'application/json',
    },
  );

  return response.statusCode == 200;
}
```

## Nouveaux Widgets

### 1. ExpiredPermitCard

Widget pour afficher une carte de permis expiré avec :
- Icône d'avertissement
- Informations du permis
- Bouton de renouvellement
- Validation automatique de propriété

### 2. ExpiredPermitsScreen

Écran dédié pour :
- Lister tous les permis expirés de l'utilisateur
- Filtrage automatique des permis expirés
- Interface de rafraîchissement
- Navigation vers le renouvellement

## Intégration API

### Endpoint de Renouvellement

```
POST https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/permits/{id}/renew
```

**Headers :**
```
Accept: application/json
Content-Type: multipart/form-data
Authorization: Bearer {token}
```

**Body (multipart/form-data) :**
```
renewalDuration: "6" (ou 12, 24)
reason: "Need more time to complete the project"
renewal_pdf: [fichier PDF]
```

### Validation de Propriété

```
GET https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/permits/{id}
```

**Réponses :**
- `200` : Permis appartient à l'utilisateur
- `403` : "You can only renew your own permits"
- `404` : Permis non trouvé

## Gestion d'Erreurs

### Erreurs d'Authentification
- Token manquant ou invalide
- Redirection vers l'écran de connexion
- Messages d'erreur localisés

### Erreurs de Validation
- Permis non expiré
- Permis n'appartenant pas à l'utilisateur
- Fichier PDF manquant ou invalide

### Erreurs Réseau
- Timeout de connexion
- Erreurs serveur
- Retry automatique avec feedback utilisateur

## Localisation

Nouvelles clés de traduction ajoutées :

**Français :**
- `expired_permits`: "Permis expirés"
- `no_expired_permits`: "Aucun permis expiré trouvé"
- `all_permits_valid`: "Tous vos permis sont encore valides"
- `renew_expired_permits`: "Renouveler les permis expirés"
- `view_expired`: "Voir les expirés"

**Arabe :**
- `expired_permits`: "التصاريح المنتهية الصلاحية"
- `no_expired_permits`: "لا توجد تصاريح منتهية الصلاحية"
- `all_permits_valid`: "جميع تصاريحك لا تزال صالحة"

**Anglais :**
- `expired_permits`: "Expired Permits"
- `no_expired_permits`: "No expired permits found"
- `all_permits_valid`: "All your permits are still valid"

## Utilisation

### 1. Accès aux Permis Expirés

Depuis le dashboard, l'utilisateur peut accéder aux permis expirés via la carte "Permis expirés" qui remplace l'ancienne carte "Renouveler la demande".

### 2. Renouvellement d'un Permis

1. L'utilisateur sélectionne un permis expiré
2. Validation automatique de la propriété
3. Navigation vers l'écran de renouvellement
4. Sélection de la durée de renouvellement
5. Upload du PDF
6. Soumission automatique avec raison constante

### 3. Sécurité

- Validation côté client et serveur
- Token d'authentification dynamique
- Vérification de propriété du permis
- Validation de l'expiration du permis

## Tests Recommandés

1. **Test de Token :**
   - Renouvellement avec token valide
   - Renouvellement avec token expiré
   - Renouvellement sans token

2. **Test de Propriété :**
   - Renouvellement de son propre permis
   - Tentative de renouvellement du permis d'autrui

3. **Test d'Expiration :**
   - Renouvellement de permis expiré
   - Tentative de renouvellement de permis valide

4. **Test d'Interface :**
   - Affichage conditionnel des permis expirés
   - Navigation entre les écrans
   - Gestion des états de chargement

## Conclusion

Ces optimisations améliorent significativement :
- La sécurité (token dynamique, validation de propriété)
- L'expérience utilisateur (interface simplifiée, validation automatique)
- La conformité API (raison constante, validation d'expiration)
- La maintenabilité (code modulaire, gestion d'erreurs)
