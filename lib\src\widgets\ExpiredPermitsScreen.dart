import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import '../widgets/locale_provider.dart';
import 'ExpiredPermitCard.dart';

class ExpiredPermitsScreen extends StatefulWidget {
  const ExpiredPermitsScreen({super.key});

  @override
  _ExpiredPermitsScreenState createState() => _ExpiredPermitsScreenState();
}

class _ExpiredPermitsScreenState extends State<ExpiredPermitsScreen> {
  final _storage = const FlutterSecureStorage();
  List<Map<String, dynamic>> _expiredPermits = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchExpiredPermits();
  }

  /// Get authentication token
  Future<String?> _getAuthToken() async {
    try {
      return await _storage.read(key: 'auth_token');
    } catch (e) {
      print('Error reading auth token: $e');
      return null;
    }
  }

  /// Fetch expired permits from API
  Future<void> _fetchExpiredPermits() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final token = await _getAuthToken();
      if (token == null) {
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'auth_token_missing') ?? 'Authentication token is missing';
          _isLoading = false;
        });
        return;
      }

      // Fetch user's permits/requests
      final response = await http.get(
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/requests'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      if (!mounted) return;

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final permits = List<Map<String, dynamic>>.from(data['data'] ?? []);
        
        // Filter for approved permits that are expired
        final expiredPermits = permits.where((permit) {
          final status = permit['status']?.toString().toLowerCase();
          final hasPayment = permit['payment'] != null;
          
          // Only consider approved and paid permits
          if (status != 'approved' || !hasPayment) return false;
          
          // Check if permit is expired
          final expiryDateStr = permit['expiry_date'] ?? permit['expiryDate'];
          if (expiryDateStr == null) return false;
          
          try {
            final expiryDate = DateTime.parse(expiryDateStr.toString());
            return DateTime.now().isAfter(expiryDate);
          } catch (e) {
            print('Error parsing expiry date: $e');
            return false;
          }
        }).toList();

        setState(() {
          _expiredPermits = expiredPermits;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to fetch permits: ${response.statusCode}';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error fetching permits: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';
    final colors = ThemeHelper.getColorsWithListener(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      backgroundColor: colors.backgroundPrimary,
      appBar: AppBar(
        title: Text(
          AppLocalizations.tr(context, 'expired_permits') ?? 'Expired Permits',
          style: ThemeHelper.getTitleStyle(context),
        ),
        backgroundColor: AppColors.primaryOrange,
        foregroundColor: AppColors.pureWhite,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? _buildErrorWidget()
              : _expiredPermits.isEmpty
                  ? _buildEmptyWidget()
                  : RefreshIndicator(
                      onRefresh: _fetchExpiredPermits,
                      child: ListView.builder(
                        padding: Constants.screenPadding,
                        itemCount: _expiredPermits.length,
                        itemBuilder: (context, index) {
                          return ExpiredPermitCard(
                            permit: _expiredPermits[index],
                            onRenewed: _fetchExpiredPermits,
                          );
                        },
                      ),
                    ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: Constants.screenPadding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: Constants.mediumSpacing),
            Text(
              _errorMessage!,
              style: ThemeHelper.getSubtitleStyle(context).copyWith(
                color: AppColors.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: Constants.largeSpacing),
            ElevatedButton(
              onPressed: _fetchExpiredPermits,
              style: ThemeHelper.getPrimaryButtonStyle(context),
              child: Text(AppLocalizations.tr(context, 'retry') ?? 'Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Padding(
        padding: Constants.screenPadding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 64,
              color: AppColors.success,
            ),
            const SizedBox(height: Constants.mediumSpacing),
            Text(
              AppLocalizations.tr(context, 'no_expired_permits') ?? 'No expired permits found',
              style: ThemeHelper.getSectionTitleStyle(context),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: Constants.smallSpacing),
            Text(
              AppLocalizations.tr(context, 'all_permits_valid') ?? 'All your permits are still valid',
              style: ThemeHelper.getSubtitleStyle(context).copyWith(
                color: ThemeHelper.getColors(context).textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: Constants.largeSpacing),
            ElevatedButton(
              onPressed: _fetchExpiredPermits,
              style: ThemeHelper.getSecondaryButtonStyle(context),
              child: Text(AppLocalizations.tr(context, 'refresh') ?? 'Refresh'),
            ),
          ],
        ),
      ),
    );
  }
}
